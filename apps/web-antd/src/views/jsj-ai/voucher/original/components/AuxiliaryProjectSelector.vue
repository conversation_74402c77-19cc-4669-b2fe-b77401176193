<script setup lang="ts">
  import { computed, ref, watch } from 'vue';

  import { PlusOutlined } from '@ant-design/icons-vue';

  import { useAccountSubjects } from '#/hooks/jsj-ai/account-book/voucher/index';

  interface Props {
    disabled?: boolean;
    modelValue?: string;
    placeholder?: string;
    selectedAccountId?: string; // 选中的会计科目ID，用于获取对应的辅助项目
  }

  interface Emits {
    (e: 'update:modelValue', value: string): void;
    (e: 'change', value: string, option: any): void;
    (e: 'addAuxiliary'): void;
  }

  const props = withDefaults(defineProps<Props>(), {
    disabled: false,
    modelValue: '',
    placeholder: '请选择辅助项目',
    selectedAccountId: '',
  });

  const emit = defineEmits<Emits>();

  // 使用会计科目 hooks
  const {
    accountSubjects,
    assistantAccounting,
    extractLeafSubjects,
    getAssistantOptions,
    loading,
  } = useAccountSubjects();

  const selectedValue = ref(props.modelValue);

  // 获取当前选中科目的信息
  const selectedAccount = computed(() => {
    console.log('🔍 AuxiliaryProjectSelector - 查找科目:', {
      accountSubjectsLength: accountSubjects.value?.length || 0,
      hasAccountSubjects: !!accountSubjects.value,
      selectedAccountId: props.selectedAccountId,
    });

    if (!props.selectedAccountId || !accountSubjects.value) return null;

    // 从叶子节点中查找科目
    const leafSubjects = extractLeafSubjects(accountSubjects.value);
    const foundSubject = leafSubjects.find((subject) => {
      // 支持数字ID和字符串ID的比较（新增科目使用字符串ID）
      return (
        subject.id === Number(props.selectedAccountId) ||
        subject.id.toString() === props.selectedAccountId
      );
    });

    console.log('📋 AuxiliaryProjectSelector - 科目查找结果:', {
      found: !!foundSubject,
      foundSubject: foundSubject
        ? {
            assistantType: foundSubject.assistantType,
            code: foundSubject.code,
            id: foundSubject.id,
            name: foundSubject.fullName,
            useAssistant: foundSubject.useAssistant,
          }
        : null,
      selectedAccountId: props.selectedAccountId,
      totalLeafSubjects: leafSubjects.length,
    });

    return foundSubject;
  });

  // 判断是否开启了辅助核算
  const isAuxiliaryEnabled = computed(() => {
    console.log('selectedAccount.value', selectedAccount.value);
    if (!selectedAccount.value) return false;
    return (
      selectedAccount.value.useAssistant === true ||
      selectedAccount.value.assistantType
    );
  });

  // 获取辅助项目选项
  const auxiliaryOptions = computed(() => {
    console.log('🎯 AuxiliaryProjectSelector - 计算辅助项目选项:', {
      hasSelectedAccount: !!selectedAccount.value,
      isAuxiliaryEnabled: isAuxiliaryEnabled.value,
      selectedAccount: selectedAccount.value
        ? {
            assistantType: selectedAccount.value.assistantType,
            id: selectedAccount.value.id,
            useAssistant: selectedAccount.value.useAssistant,
          }
        : null,
    });

    if (!isAuxiliaryEnabled.value || !selectedAccount.value) {
      console.log('⚠️ 辅助核算未启用或未选择科目，返回空选项');
      return [];
    }

    // 获取该科目的辅助核算选项
    const assistantType = selectedAccount.value.assistantType;
    console.log('🔧 获取辅助核算选项:', {
      assistantAccountingKeys: assistantAccounting.value
        ? Object.keys(assistantAccounting.value)
        : [],
      assistantType,
      hasAssistantAccounting: !!assistantAccounting.value,
    });

    if (assistantType && assistantAccounting.value) {
      const options = getAssistantOptions(assistantType);
      console.log('📊 获取到的辅助核算选项:', {
        assistantType,
        options: options.slice(0, 3), // 只显示前3个，避免日志过长
        optionsCount: options.length,
      });

      // 适配 antd select 的 options 格式
      const mappedOptions = options.map((item) => ({
        ...item,
        label: `${item.code} ${item.name}`,
        value: item.id.toString(), // 转换为字符串以保持类型一致性
      }));

      // 添加"无辅助项目"选项
      const finalOptions = [
        { code: '', label: '无辅助项目', name: '无辅助项目', value: '' },
        ...mappedOptions,
      ];

      console.log('✅ 最终辅助项目选项:', {
        firstFewOptions: finalOptions
          .slice(0, 3)
          .map((opt) => ({ label: opt.label, value: opt.value })),
        hasNoAuxiliaryOption: finalOptions[0]?.label === '无辅助项目',
        totalCount: finalOptions.length,
      });

      return finalOptions;
    }

    console.log('⚠️ 无辅助核算类型或数据，返回默认选项');
    return [{ code: '', label: '无辅助项目', name: '无辅助项目', value: '' }];
  });

  // 显示的占位符文本
  const displayPlaceholder = computed(() => {
    if (!props.selectedAccountId) {
      return '请先选择会计科目';
    }
    if (!isAuxiliaryEnabled.value) {
      return '未开启辅助核算';
    }
    return props.placeholder;
  });

  // 是否禁用选择框
  const isDisabled = computed(() => {
    console.log('111', props.disabled);
    console.log('222', props.selectedAccountId);
    console.log('333', isAuxiliaryEnabled.value);
    return (
      props.disabled || !props.selectedAccountId || !isAuxiliaryEnabled.value
    );
  });

  // 搜索过滤函数
  const filterOption = (input: string, option: any) => {
    const searchText = input.toLowerCase();
    return (
      option.label.toLowerCase().includes(searchText) ||
      (option.code && option.code.toLowerCase().includes(searchText)) ||
      (option.name && option.name.toLowerCase().includes(searchText))
    );
  };

  // 选择变化处理
  const handleChange = (value: any, option?: any) => {
    const stringValue = value ? String(value) : '';
    selectedValue.value = stringValue;
    emit('update:modelValue', stringValue);

    if (option) {
      emit('change', stringValue, option);
    }
  };

  // 新增辅助项目
  const handleAddAuxiliary = () => {
    emit('addAuxiliary');
  };

  // 监听外部值变化
  watch(
    () => props.modelValue,
    (newValue) => {
      selectedValue.value = newValue;
    },
    { immediate: true },
  );

  // 监听选中科目变化，清空辅助项目选择
  watch(
    () => props.selectedAccountId,
    (newAccountId, oldAccountId) => {
      // 只有在科目真正发生变化时才清空辅助项目，避免初始化时被清空
      if (oldAccountId !== undefined && newAccountId !== oldAccountId) {
        selectedValue.value = '';
        emit('update:modelValue', '');
      }
    },
  );
</script>

<template>
  <div class="auxiliary-project-selector">
    <a-select
      v-model:value="selectedValue"
      :placeholder="displayPlaceholder"
      show-search
      allow-clear
      :disabled="isDisabled"
      :loading="loading"
      :options="auxiliaryOptions"
      :filter-option="filterOption"
      style="width: 100%"
      @change="handleChange"
    >
      <template #notFoundContent>
        <div v-if="loading">
          <a-spin size="small" />
          加载中...
        </div>
        <div v-else-if="!props.selectedAccountId">请先选择会计科目</div>
        <div v-else-if="!isAuxiliaryEnabled">未开启辅助核算</div>
        <div v-else>暂无数据</div>
      </template>

      <!-- 下拉框底部的新增辅助项目按钮 -->
      <template #dropdownRender="{ menuNode }">
        <div v-if="isAuxiliaryEnabled && props.selectedAccountId">
          <component :is="menuNode" />
          <a-divider style="margin: 4px 0" />
          <div
            style="
              display: flex;
              align-items: center;
              justify-content: center;
              padding: 4px 8px;
              color: #1890ff;
              cursor: pointer;
            "
            @click="handleAddAuxiliary"
          >
            <PlusOutlined style="margin-right: 4px" />
            新增辅助项目
          </div>
        </div>
        <div v-else>
          <component :is="menuNode" />
        </div>
      </template>
    </a-select>
  </div>
</template>

<style lang="scss" scoped>
  .auxiliary-project-selector {
    width: 100%;
  }
</style>
