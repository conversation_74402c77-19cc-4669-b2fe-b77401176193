<script lang="ts" setup>
  import { reactive, ref } from 'vue';

  import { message } from 'ant-design-vue';

  import { useAccountSubjects } from '#/hooks/jsj-ai/account-book/voucher/index';

  import emitter from './usermitt';

  defineOptions({
    name: 'AddSubjectPop',
  });

  const emits = defineEmits(['refresh']);

  const formRef = ref<any>(null);
  const { pureSubjectOptions } = useAccountSubjects();
  const loading = ref(false);
  const visible = ref(false);

  const formState = reactive({
    assistantType: 'customer', // 辅助核算类型，默认客户
    code: '404', // 科目编码，默认404
    name: '', // 科目名称
    parentId: '', // 上级科目id
    useAssistant: true, // 是否启用辅助核算，默认启用
  });

  // 辅助核算类型选项
  const auxiliaryTypeOptions = [
    { label: '客户', value: 'customer' },
    { label: '供应商', value: 'supplier' },
    { label: '员工', value: 'employee' },
    { label: '部门', value: 'department' },
    { label: '项目', value: 'project' },
    { label: '存货', value: 'inventory' },
  ];

  // 重置表单
  const resetForm = () => {
    formState.code = '404';
    formState.name = '';
    formState.parentId = '';
    formState.useAssistant = true;
    formState.assistantType = 'customer';
  };

  // 打开弹框
  const open = (currentSubject?: any) => {
    resetForm();
    console.log('🔍 接收到的当前科目数据:', currentSubject);
    console.log('📋 可用的上级科目选项:', pureSubjectOptions.value);

    // 处理当前科目作为上级科目的默认值
    if (currentSubject) {
      let subjectId = '';

      // 处理新版科目选择器格式（有id字段）
      if (currentSubject.id) {
        subjectId = currentSubject.id.toString();
      }
      // 处理旧版科目选择器格式（可能有value字段）
      else if (currentSubject.value) {
        subjectId = currentSubject.value.toString();
      }
      // 处理VoucherEditing中的科目对象格式（科目信息在text字段中）
      else if (currentSubject.text) {
        // 解析text字段，格式如："1122 应收账款"
        const textParts = currentSubject.text.split(' ').filter(Boolean);
        if (textParts.length >= 2) {
          const code = textParts[0];
          const name = textParts.slice(1).join(' ');

          // 从pureSubjectOptions中查找匹配的科目
          const matchedSubject = pureSubjectOptions.value.find(
            (option: any) => {
              // 检查科目代码和名称是否匹配
              return option.code === code && option.name === name;
            },
          );

          if (matchedSubject) {
            subjectId = matchedSubject.value;
            console.log('✅ 从text字段解析并匹配到科目:', {
              code,
              name,
              subjectId,
            });
          } else {
            console.warn('⚠️ 从text字段解析科目但未找到匹配项:', {
              code,
              name,
            });
          }
        }
      }
      // 处理其他可能的格式
      else if (typeof currentSubject === 'string') {
        subjectId = currentSubject;
      }

      if (subjectId) {
        // 提取纯科目ID（去掉辅助核算的组合ID）
        const pureSubjectId = subjectId.split('_')[0];
        formState.parentId = pureSubjectId || '';
        console.log('✅ 设置上级科目ID:', pureSubjectId);

        // 验证ID是否在选项中存在
        const foundOption = pureSubjectOptions.value.find(
          (option) => option.value === pureSubjectId,
        );
        if (foundOption) {
          console.log('✅ 找到匹配的上级科目选项:', foundOption);
        } else {
          console.warn('⚠️ 未找到匹配的上级科目选项，ID:', pureSubjectId);
          console.log(
            '📋 所有可用选项:',
            pureSubjectOptions.value.map((opt) => ({
              label: opt.label,
              value: opt.value,
            })),
          );
        }
      } else {
        console.warn('⚠️ 无法从当前科目数据中提取有效的科目ID');
      }
    }

    visible.value = true;
  };
  // 确认保存
  const handleOk = async () => {
    try {
      loading.value = true;
      await formRef.value.validate();

      // 查找上级科目以获取其信息
      const parentSubject = pureSubjectOptions.value.find(
        (option) => option.value === formState.parentId,
      ) as any;

      // 获取上级科目的code和name
      const parentCode = parentSubject ? parentSubject.code : '';
      const parentName = parentSubject ? parentSubject.name : '';

      // 生成唯一ID，确保id和value一致
      const uniqueId = Date.now().toString();

      // 构建新科目对象
      const newSubject = {
        // 新增字段：标记这是新增的科目
        account: formState.name,
        // 为了与现有类型兼容，我们添加一些默认属性
        assistAccounting: null,
        assistantType: formState.useAssistant
          ? formState.assistantType
          : undefined, // 辅助核算类型
        balance: 0,
        balanceDirection: 'debit', // 或 'credit'，根据需要设置默认值
        code: formState.code,
        currency: null,
        // 科目名称单独显示，不拼接上级科目
        fullName: formState.name,
        id: uniqueId, // 使用统一的唯一ID
        isForCurrency: false,
        level: parentSubject ? (parentSubject.level || 1) + 1 : 1,
        name: formState.name,
        // 新增字段：存储上级科目的code
        p_account_code: parentCode,
        // 新增字段：存储上级科目的名称（可选，用于显示）
        p_account_name: parentName,
        // 显示文本也只显示新增科目本身
        text: `${formState.code} ${formState.name}`,
        useAssistant: formState.useAssistant, // 使用表单中的辅助核算设置
        value: uniqueId, // 确保value与id一致
        children: [],
      };

      message.success('新增科目成功');
      // 通知凭证页面刷新科目数据
      emitter.emit('account_voucher_subject_added', newSubject);
      // 通知父组件刷新
      emits('refresh');
      // 关闭弹框
      visible.value = false;
    } catch (error) {
      console.error('新增科目失败:', error);
      message.error('新增科目失败');
    } finally {
      loading.value = false;
    }
  };

  // 取消
  const handleCancel = () => {
    visible.value = false;
  };

  // 暴露方法给父组件
  defineExpose({
    open,
  });
</script>
<template>
  <a-modal
    :visible="visible"
    title="新增科目"
    :confirm-loading="loading"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <a-form
      :model="formState"
      autocomplete="off"
      ref="formRef"
      :label-col="{ span: 5 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item
        label="上级科目"
        name="parentId"
        :rules="[{ required: true, message: '请选择上级科目' }]"
      >
        <a-select
          v-model:value="formState.parentId"
          placeholder="请选择上级科目"
          show-search
          :field-names="{
            label: 'label',
            value: 'value',
          }"
          :options="pureSubjectOptions"
          @change="(value) => console.log('上级科目选择变化:', value)"
        />
      </a-form-item>
      <a-form-item label="科目编码" name="code">
        <a-input
          v-model:value="formState.code"
          placeholder="默认404"
          disabled
        />
      </a-form-item>
      <a-form-item
        label="科目名称"
        name="name"
        :rules="[{ required: true, message: '请输入科目名称' }]"
      >
        <a-input v-model:value="formState.name" placeholder="请输入科目名称" />
      </a-form-item>
      <a-form-item label="启用辅助核算" name="useAssistant">
        <a-switch v-model:checked="formState.useAssistant" />
      </a-form-item>
      <a-form-item
        v-if="formState.useAssistant"
        label="辅助核算类型"
        name="assistantType"
        :rules="[{ required: true, message: '请选择辅助核算类型' }]"
      >
        <a-select
          v-model:value="formState.assistantType"
          placeholder="请选择辅助核算类型"
          :options="auxiliaryTypeOptions"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<style lang="scss" scoped>
  .btn {
    text-align: center;
  }

  .reminder {
    font-size: 10px;
    color: red;
  }
</style>
